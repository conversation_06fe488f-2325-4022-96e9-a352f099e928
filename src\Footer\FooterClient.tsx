'use client'

import Link from 'next/link'
import React from 'react'
import { motion } from 'framer-motion'
import { Mail, Phone, MapPin, ExternalLink } from 'lucide-react'

import type { Footer } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Logo } from '@/components/Logo/Logo'

interface FooterClientProps {
  navItems: Footer['navItems']
}

export function FooterClient({ navItems }: FooterClientProps) {
  return (
    <footer className="mt-auto border-t-4 border-gradient-to-r from-[#4C6444] to-[#8A6240] bg-gradient-to-br from-[#E5E1DC] via-[#CEC9BC] to-[#CABA9C] text-foreground relative overflow-hidden">
      {/* Decorative Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 w-40 h-40 bg-gradient-to-br from-[#6E3C19] to-[#8A6240] rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-48 h-48 bg-gradient-to-br from-[#4C6444] to-[#102820] rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-gradient-to-br from-[#A7795E] to-[#CABA9C] rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute top-20 right-1/3 w-28 h-28 bg-gradient-to-br from-[#8D8F78] to-[#CEC9BC] rounded-full blur-2xl animate-pulse"></div>
      </div>
      <div className="relative z-10 npi-container py-20">
        {/* Main Footer Content */}
        <div className="grid md:grid-cols-5 gap-12 mb-16">
          {/* Logo and Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="md:col-span-2"
          >
            <Link className="flex items-center mb-8 group" href="/">
              <Logo className="text-foreground group-hover:scale-105 transition-transform duration-300" />
            </Link>
            <p className="text-muted-foreground mb-8 font-npi text-lg leading-[1.7] max-w-md">
              Harnessing Indigenous Wealth for Sustainable Growth. Transforming Kenya&apos;s rich
              natural heritage into sustainable economic opportunities through innovation and
              community empowerment.
            </p>
            {/* Social Media Links - Skeleton Style with Color Palette */}
            <div className="flex gap-4">
              <motion.a
                whileHover={{ scale: 1.15, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                href="https://facebook.com/npikenya"
                target="_blank"
                rel="noopener noreferrer"
                className="group transition-all duration-300"
                aria-label="Follow us on Facebook"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-[#4C6444] to-[#102820] hover:from-[#8A6240] hover:to-[#A7795E] flex items-center justify-center transition-all duration-300 group-hover:shadow-xl group-hover:shadow-[#4C6444]/50 border-2 border-[#8D8F78] hover:border-[#A7795E]">
                  <svg
                    className="w-5 h-5 text-[#E5E1DC] group-hover:text-[#CEC9BC] transition-colors"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                  </svg>
                </div>
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.15, rotate: -5 }}
                whileTap={{ scale: 0.95 }}
                href="https://twitter.com/npikenya"
                target="_blank"
                rel="noopener noreferrer"
                className="group transition-all duration-300"
                aria-label="Follow us on Twitter"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-[#8A6240] to-[#6E3C19] hover:from-[#A7795E] hover:to-[#CABA9C] flex items-center justify-center transition-all duration-300 group-hover:shadow-xl group-hover:shadow-[#8A6240]/50 border-2 border-[#A7795E] hover:border-[#CABA9C]">
                  <svg
                    className="w-5 h-5 text-[#E5E1DC] group-hover:text-[#CEC9BC] transition-colors"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z" />
                  </svg>
                </div>
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.15, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                href="https://linkedin.com/company/npikenya"
                target="_blank"
                rel="noopener noreferrer"
                className="group transition-all duration-300"
                aria-label="Connect with us on LinkedIn"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-[#A7795E] to-[#CABA9C] hover:from-[#4C6444] hover:to-[#102820] flex items-center justify-center transition-all duration-300 group-hover:shadow-xl group-hover:shadow-[#A7795E]/50 border-2 border-[#CABA9C] hover:border-[#4C6444]">
                  <svg
                    className="w-5 h-5 text-[#E5E1DC] group-hover:text-[#CEC9BC] transition-colors"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                    <rect x="2" y="9" width="4" height="12" />
                    <circle cx="4" cy="4" r="2" />
                  </svg>
                </div>
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.15, rotate: -5 }}
                whileTap={{ scale: 0.95 }}
                href="https://instagram.com/npikenya"
                target="_blank"
                rel="noopener noreferrer"
                className="group transition-all duration-300"
                aria-label="Follow us on Instagram"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-[#6E3C19] to-[#34170D] hover:from-[#8A6240] hover:to-[#A7795E] flex items-center justify-center transition-all duration-300 group-hover:shadow-xl group-hover:shadow-[#6E3C19]/50 border-2 border-[#8A6240] hover:border-[#A7795E]">
                  <svg
                    className="w-5 h-5 text-[#E5E1DC] group-hover:text-[#CEC9BC] transition-colors"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <rect x="2" y="2" width="20" height="20" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" />
                  </svg>
                </div>
              </motion.a>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h3 className="text-foreground font-bold text-lg mb-6 font-npi"></h3>
            <nav className="flex flex-col gap-3">
              {navItems?.slice(0, 5).map(({ link }, i) => {
                return (
                  <motion.div
                    key={i}
                    whileHover={{ x: 5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <CMSLink
                      className="text-muted-foreground hover:text-primary transition-all duration-300 font-npi flex items-center gap-2 group font-medium text-sm"
                      {...link}
                    >
                      <ExternalLink className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </CMSLink>
                  </motion.div>
                )
              })}
            </nav>
          </motion.div>

          {/* Resources */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <h3 className="text-foreground font-bold text-lg mb-6 font-npi">Resources</h3>
            <ul className="space-y-3">
              {[
                { label: 'IKIA Database', href: 'http://inkibank.museums.or.ke:8185/' },
                { label: 'Publications', href: '/publications' },
                { label: 'Training Materials', href: '/training' },
                { label: 'Policy Documents', href: '/policy' },
                { label: 'Downloads', href: '/downloads' },
              ].map((link, index) => (
                <motion.li
                  key={index}
                  whileHover={{ x: 5 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  {link.href.startsWith('http') ? (
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors duration-300 font-medium text-sm"
                    >
                      {link.label}
                    </a>
                  ) : (
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-primary transition-colors duration-300 font-medium text-sm"
                    >
                      {link.label}
                    </Link>
                  )}
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <h3 className="text-foreground font-bold text-lg mb-6 font-npi">Contact</h3>
            <div className="text-muted-foreground space-y-5 font-npi">
              <motion.div
                whileHover={{ x: 5 }}
                className="flex items-start gap-4 group cursor-pointer"
              >
                <MapPin className="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-300 mt-0.5" />
                <div>
                  <p className="font-semibold text-foreground">
                    Natural Products Industry Initiative
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Kenya Industrial Research and Development Institute (KIRDI)
                    <br />
                    P.O. Box 30650-00100, Nairobi, Kenya
                  </p>
                </div>
              </motion.div>
              <motion.div whileHover={{ x: 5 }} className="flex items-center gap-4 group">
                <Mail className="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-300" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-foreground hover:text-primary transition-colors font-medium"
                >
                  <EMAIL>
                </a>
              </motion.div>
              <motion.div whileHover={{ x: 5 }} className="flex items-center gap-4 group">
                <Phone className="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-300" />
                <a
                  href="tel:+254203742131"
                  className="text-foreground hover:text-primary transition-colors font-medium"
                >
                  +254 20 374 2131
                </a>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </footer>
  )
}
