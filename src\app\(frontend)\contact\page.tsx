import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'Contact Us - Natural Products Industry Initiative',
  description:
    "Get in touch with the NPI team. Contact us for partnerships, research collaboration, IKIA database access, or general inquiries about Kenya's natural products development.",
}

const contactPageLayout = [
  {
    blockType: 'npiContactForm' as const,
  },
]

export default function ContactPage() {
  return (
    <>
      <PageClient />
      <article className="pb-24">
        <RenderBlocks blocks={contactPageLayout} />
      </article>
    </>
  )
}
